// User authentication types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  user: User;
}

export interface User {
  username: string;
  role: string;
}

// User management types
export interface UserListParams {
  include_deleted?: boolean;
  page?: number;
  limit?: number;
}

export interface UserListResponse {
  data: User[];
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}

// API response types
export interface ApiError {
  detail: string;
}
