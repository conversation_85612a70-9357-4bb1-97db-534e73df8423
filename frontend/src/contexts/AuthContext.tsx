import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useMemo,
  type ReactNode,
} from 'react';
import type { User } from '../types';
import { getUserIdFromToken, isTokenExpired } from '../utils/jwt';
import { apiService } from '../services/api';

interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (access_token: string, refresh_token: string, userData?: User) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // <PERSON><PERSON><PERSON> tra token khi app khởi động và fetch user theo ID từ token
  useEffect(() => {
    const initializeAuth = async () => {
      const access_token = localStorage.getItem('access_token');

      if (access_token && !isTokenExpired(access_token)) {
        try {
          const userId = getUserIdFromToken(access_token);
          if (userId) {
            const userData = await apiService.getUserById(userId);
            setUser(userData);
            localStorage.setItem('user', JSON.stringify(userData));
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          // Nếu có lỗi, xóa token và user data
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');
          setUser(null);
        }
      } else {
        // Token hết hạn hoặc không tồn tại
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        setUser(null);
      }

      setIsLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (access_token: string, refresh_token: string, userData?: User) => {
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);

    try {
      // Lấy user ID từ token và fetch thông tin user
      const userId = getUserIdFromToken(access_token);
      if (userId) {
        const userFromApi = await apiService.getUserById(userId);
        setUser(userFromApi);
        localStorage.setItem('user', JSON.stringify(userFromApi));
      } else if (userData) {
        // Fallback nếu không thể lấy ID từ token
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Error fetching user data during login:', error);
      // Fallback sử dụng userData từ response nếu có
      if (userData) {
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
      }
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    setUser(null);
  };

  const isAuthenticated = !!user && !!localStorage.getItem('access_token');

  const value = useMemo(
    () => ({
      user,
      setUser,
      isAuthenticated,
      isLoading,
      login,
      logout,
    }),
    [user, isAuthenticated, isLoading]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
