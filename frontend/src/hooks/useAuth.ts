import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '../contexts/AuthContext';
import { apiService } from '../services/api';
import type { LoginRequest, RegisterRequest } from '../types';

interface AxiosError {
  response?: {
    data?: {
      detail?: string;
    };
  };
}

export const useAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const {
    login: contextLogin,
    logout: contextLogout,
    user,
    isAuthenticated,
  } = useAuthContext();

  const login = async (data: LoginRequest) => {
    try {
      console.log('🔵 Starting login process...');
      setIsLoading(true);
      setError(null);

      console.log('🔵 Calling apiService.login...');
      const response = await apiService.login(data);
      console.log('🔵 Login response received:', response);

      // Đơn giản hóa: chỉ lưu token và user data, không fetch thêm
      console.log('🔵 Saving tokens and user data...');
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('refresh_token', response.refresh_token);
      localStorage.setItem('user', JSON.stringify(response.user));

      console.log('🔵 Navigating to dashboard...');
      navigate('/dashboard');
      console.log('🔵 Login process completed successfully');
      return response;
    } catch (err) {
      console.error('🔴 Login error:', err);
      const axiosError = err as AxiosError;
      const errorMessage =
        axiosError.response?.data?.detail || 'Đăng nhập thất bại';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterRequest) => {
    try {
      setIsLoading(true);
      setError(null);
      await apiService.register(data);
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (err) {
      const axiosError = err as AxiosError;
      const errorMessage =
        axiosError.response?.data?.detail || 'Đăng ký thất bại';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    contextLogout();
    navigate('/login');
  };

  return {
    login,
    register,
    logout,
    isAuthenticated,
    user,
    isLoading,
    error,
    clearError: () => setError(null),
  };
};
