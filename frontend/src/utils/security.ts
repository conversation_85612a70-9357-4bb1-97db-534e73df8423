import type { PasswordValidation } from '../types/auth';
import SecureTokenStorage from './tokenStorage';

// Create validation utilities
export const validatePassword = (password: string): PasswordValidation => {
  const minLength = password.length >= 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  return {
    minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChars,
    isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars,
  };
};

export const sanitizeInput = (input: string): string => {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/[<>'"]/g, (char) => {
      const entities: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
      };
      return entities[char] || char;
    })
    .trim();
};

export const validateUsername = (username: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeInput(username);
  
  if (sanitized.length < 3) {
    return { isValid: false, error: 'Tên đăng nhập phải có ít nhất 3 ký tự' };
  }
  
  if (sanitized.length > 50) {
    return { isValid: false, error: 'Tên đăng nhập không được quá 50 ký tự' };
  }
  
  if (!/^[a-zA-Z0-9_.-]+$/.test(sanitized)) {
    return { isValid: false, error: 'Tên đăng nhập chỉ được chứa chữ cái, số và ký tự _.-, @' };
  }
  
  return { isValid: true };
};

// Enhanced rate limiting with encrypted storage
export const checkRateLimit = (maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000): boolean => {
  const now = Date.now();
  const attempts = SecureTokenStorage.getSecureData('login_attempts', true);
  
  if (attempts) {
    const { count, timestamp } = attempts;
    
    if (now - timestamp < windowMs) {
      if (count >= maxAttempts) {
        return false; // Rate limited
      }
    } else {
      // Reset window - remove old data
      SecureTokenStorage.setSecureData('login_attempts', null, true);
    }
  }
  
  return true;
};

export const recordLoginAttempt = (success: boolean): void => {
  const now = Date.now();
  const storedAttempts = SecureTokenStorage.getSecureData('login_attempts', true);
  
  if (success) {
    // Clear failed attempts on success
    SecureTokenStorage.setSecureData('login_attempts', null, true);
    return;
  }
  
  let count = 1;
  if (storedAttempts) {
    const { count: prevCount, timestamp } = storedAttempts;
    if (now - timestamp < 15 * 60 * 1000) {
      count = prevCount + 1;
    }
  }
  
  SecureTokenStorage.setSecureData('login_attempts', { count, timestamp: now }, true);
};

// Device fingerprinting helper
export const generateDeviceFingerprint = (): string => {
  const deviceInfo = {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    screen: `${screen.width}x${screen.height}x${screen.colorDepth}`,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
    doNotTrack: navigator.doNotTrack || 'unknown',
  };
  
  return SecureTokenStorage.generateSecureId();
};

// JWT Decoding utility (với proper types)
export const decodeJWT = (token: string): Record<string, unknown> | null => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    const decodedBytes = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    const decodedPayload = JSON.parse(decodedBytes);
    
    return decodedPayload;
  } catch (error) {
    console.error('Failed to decode JWT:', error);
    return null;
  }
};

// Extract user info from JWT token (với proper types)
export const extractUserFromToken = (token: string): Record<string, unknown> | null => {
  const decoded = decodeJWT(token);
  if (!decoded) return null;
  
  return {
    id: decoded.sub || decoded.user_id || decoded.id,
    username: decoded.username || decoded.name,
    role: decoded.role,
    email: decoded.email,
    created_at: decoded.created_at || (decoded.iat ? new Date((decoded.iat as number) * 1000).toISOString() : new Date().toISOString()),
    expires_at: decoded.exp ? new Date((decoded.exp as number) * 1000).toISOString() : null,
    issued_at: decoded.iat ? new Date((decoded.iat as number) * 1000).toISOString() : null,
  };
};