import CryptoJS from 'crypto-js';

// JWT Decoding utility
export const decodeJWT = (token: string): Record<string, unknown> | null => {
  try {
    // JWT có format: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    // Decode base64 payload (phần thứ 2)
    const payload = parts[1];
    // Thê<PERSON> padding nếu cần
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode base64 thành JSON
    const decodedBytes = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    const decodedPayload = JSON.parse(decodedBytes);
    
    return decodedPayload;
  } catch (error) {
    console.error('Failed to decode JWT:', error);
    return null;
  }
};

// Extract user info from JWT token
export const extractUserFromToken = (token: string): Record<string, unknown> | null => {
  const decoded = decodeJWT(token);
  if (!decoded) return null;
  
  return {
    id: decoded.sub || decoded.user_id || decoded.id,
    username: decoded.username || decoded.name,
    role: decoded.role,
    email: decoded.email,
    created_at: decoded.created_at || (decoded.iat ? new Date((decoded.iat as number) * 1000).toISOString() : null),
    expires_at: decoded.exp ? new Date((decoded.exp as number) * 1000).toISOString() : null,
    issued_at: decoded.iat ? new Date((decoded.iat as number) * 1000).toISOString() : null,
  };
};

// Token storage utility với mã hóa AES
class SecureTokenStorage {
  private static readonly ACCESS_TOKEN_KEY = 'app_session';
  private static readonly REFRESH_TOKEN_KEY = 'app_refresh';
  private static readonly USER_DATA_KEY = 'user_context';
  private static readonly ENCRYPTION_KEY = 'your-app-secret-key-2025';

  // Mã hóa dữ liệu trước khi lưu
  private static encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString();
  }

  // Giải mã dữ liệu khi đọc
  private static decrypt(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  // Access Token - lưu trong sessionStorage với mã hóa
  static setAccessToken(token: string): void {
    const encrypted = this.encrypt(token);
    sessionStorage.setItem(this.ACCESS_TOKEN_KEY, encrypted);
  }

  static getAccessToken(): string | null {
    const encrypted = sessionStorage.getItem(this.ACCESS_TOKEN_KEY);
    if (!encrypted) return null;
    
    try {
      return this.decrypt(encrypted);
    } catch {
      this.removeAccessToken();
      return null;
    }
  }

  static removeAccessToken(): void {
    sessionStorage.removeItem(this.ACCESS_TOKEN_KEY);
  }

  // Refresh Token methods
  static setRefreshToken(token: string): void {
    const encrypted = this.encrypt(token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, encrypted);
  }

  static getRefreshToken(): string | null {
    const encrypted = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    if (!encrypted) return null;
    
    try {
      return this.decrypt(encrypted);
    } catch {
      this.removeRefreshToken();
      return null;
    }
  }

  static removeRefreshToken(): void {
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  // Token expiry tracking
  static setTokenExpiry(expiresIn: number): void {
    const expiryTime = Date.now() + (expiresIn * 1000);
    localStorage.setItem('token_expiry', expiryTime.toString());
  }

  static getTokenExpiry(): number | null {
    const expiry = localStorage.getItem('token_expiry');
    return expiry ? parseInt(expiry) : null;
  }

  static isTokenExpired(): boolean {
    const expiry = this.getTokenExpiry();
    return expiry ? Date.now() >= expiry : true;
  }

  // User data với mã hóa
  static setUserData(user: Record<string, unknown>): void {
    const encrypted = this.encrypt(JSON.stringify(user));
    sessionStorage.setItem(this.USER_DATA_KEY, encrypted);
  }

  static getUserData(): Record<string, unknown> | null {
    const encrypted = sessionStorage.getItem(this.USER_DATA_KEY);
    if (!encrypted) return null;
    
    try {
      const decrypted = this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch {
      this.removeUserData();
      return null;
    }
  }

  static removeUserData(): void {
    sessionStorage.removeItem(this.USER_DATA_KEY);
  }

  // Secure storage cho dữ liệu tạm thời
  static setSecureData(key: string, data: unknown, persistent = false): void {
    const encrypted = this.encrypt(JSON.stringify(data));
    const storage = persistent ? localStorage : sessionStorage;
    storage.setItem(`secure_${key}`, encrypted);
  }

  static getSecureData(key: string, persistent = false): unknown | null {
    const storage = persistent ? localStorage : sessionStorage;
    const encrypted = storage.getItem(`secure_${key}`);
    if (!encrypted) return null;
    
    try {
      const decrypted = this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch {
      storage.removeItem(`secure_${key}`);
      return null;
    }
  }

  // Generate secure random string
  static generateSecureId(): string {
    return CryptoJS.lib.WordArray.random(16).toString();
  }

  // Clear all stored data
  static clearAll(): void {
    sessionStorage.removeItem(this.ACCESS_TOKEN_KEY);
    sessionStorage.removeItem(this.USER_DATA_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem('token_expiry');
    
    // Clear any old unencrypted tokens
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Check authentication status
  static isLoggedIn(): boolean {
    const hasAccessToken = !!this.getAccessToken();
    const hasUserData = !!this.getUserData();
    const isNotExpired = !this.isTokenExpired();
    
    return hasAccessToken && hasUserData && isNotExpired;
  }

  // Enhanced user data với thông tin từ JWT
  static setUserDataFromToken(token: string): void {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return;
      
      const payload = parts[1];
      const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
      const decodedBytes = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
      const decodedPayload = JSON.parse(decodedBytes);
      
      const userInfo = {
        id: decodedPayload.sub || decodedPayload.user_id || decodedPayload.id || Math.floor(Math.random() * 1000),
        username: decodedPayload.username || decodedPayload.name,
        role: decodedPayload.role || 'user',
        email: decodedPayload.email,
        created_at: decodedPayload.created_at || (decodedPayload.iat ? new Date(decodedPayload.iat * 1000).toISOString() : new Date().toISOString()),
        updated_at: decodedPayload.updated_at || new Date().toISOString(),
        expires_at: decodedPayload.exp ? new Date(decodedPayload.exp * 1000).toISOString() : null,
      };
      
      if (userInfo.username) {
        const encrypted = this.encrypt(JSON.stringify(userInfo));
        sessionStorage.setItem(this.USER_DATA_KEY, encrypted);
      }
    } catch (error) {
      console.error('Failed to extract user from token:', error);
    }
  }
}

export default SecureTokenStorage;
